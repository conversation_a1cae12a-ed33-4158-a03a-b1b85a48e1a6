import 'package:flutter/material.dart';
import '../tabs/add_house_tab.dart';
import '../tabs/parameter_input_tab.dart';
import '../tabs/price_settings_tab.dart';
import '../theme/construction_theme.dart';

/// <PERSON><PERSON><PERSON> h<PERSON>nh ch<PERSON>h với các tab
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    // Khởi tạo TabController với 3 tab
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    // Giải phóng TabController khi widget bị hủy
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dự toán vật tư xây dựng'),
        backgroundColor: ConstructionTheme.primaryColor,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.home), text: 'Thêm nhà'),
            Tab(icon: Icon(Icons.input), text: 'Nhập thông số'),
            Tab(icon: Icon(Icons.settings), text: 'Cài đặt giá'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [AddHouseTab(), ParameterInputTab(), PriceSettingsTab()],
      ),
    );
  }
}
