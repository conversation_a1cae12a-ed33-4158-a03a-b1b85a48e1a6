import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/project/project_provider.dart';
import '../models/material_provider.dart';
import '../models/material_model.dart' as model;
import '../utils/number_formatter.dart';
import 'project_wizard/project_wizard_screen.dart';
import 'project_detail_screen.dart';

/// <PERSON><PERSON>n hình ch<PERSON>h mới theo thiết kế request10.md
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentPage = 0;
  int _selectedMenuIndex = 0; // 0: Th<PERSON> viện, 1: <PERSON>h<PERSON><PERSON> mới, 2: <PERSON><PERSON><PERSON> kh<PERSON><PERSON> lượng

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF6A3DE8), Color(0xFF4A3DE8)],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(),

              // Project List với PageView
              _buildProjectList(),

              // Page indicator
              _buildPageIndicator(),

              SizedBox(height: 20),

              // Menu options (3 nút)
              _buildMenuOptions(),

              SizedBox(height: 20),

              // Nội dung dựa trên menu được chọn
              Expanded(child: _buildSelectedContent()),
            ],
          ),
        ),
      ),
    );
  }

  /// Xây dựng header với nút quay lại và menu
  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          IconButton(
            icon: Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          Text(
            'Danh sách công trình',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          Spacer(),
          IconButton(
            icon: Icon(Icons.more_vert, color: Colors.white),
            onPressed: () {
              // Xử lý khi nhấn nút menu
            },
          ),
        ],
      ),
    );
  }

  /// Xây dựng danh sách dự án với PageView
  Widget _buildProjectList() {
    return Consumer<ProjectProvider>(
      builder: (context, projectProvider, child) {
        final projects = projectProvider.projects;

        if (projects.isEmpty) {
          return _buildEmptyProjectState();
        }

        return SizedBox(
          height: 350, // Giảm chiều cao để tránh overflow
          child: PageView.builder(
            onPageChanged: (int page) {
              setState(() {
                _currentPage = page;
              });
            },
            itemCount: projects.length,
            itemBuilder: (context, index) {
              final project = projects[index];
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Card(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Project image
                      ClipRRect(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20),
                        ),
                        child: Container(
                          height: 180, // Giảm chiều cao ảnh
                          color: Colors.grey[300],
                          child:
                              project.imagePath != null &&
                                      project.imagePath!.isNotEmpty
                                  ? Image.file(
                                    File(project.imagePath!),
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return _buildDefaultProjectImage();
                                    },
                                  )
                                  : _buildDefaultProjectImage(),
                        ),
                      ),

                      // Project info
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              project.name,
                              style: TextStyle(
                                fontSize: 20, // Giảm từ 32 xuống 20
                                fontWeight: FontWeight.bold,
                                color: Colors.pink[100],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(height: 4),
                            Text(
                              project.location,
                              style: TextStyle(
                                fontSize: 14, // Giảm từ 20 xuống 14
                                color: Colors.grey,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),

                      // Ngày tạo và Edit button
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 16.0,
                          right: 16.0,
                          bottom: 16.0,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Ngày tạo dự án (căn lề trái)
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.calendar_today,
                                    size: 14,
                                    color: Colors.grey[600],
                                  ),
                                  SizedBox(width: 4),
                                  Text(
                                    '${project.createdAt.day}/${project.createdAt.month}/${project.createdAt.year}',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey[600],
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // Edit button (căn lề phải)
                            GestureDetector(
                              onTap: () {
                                projectProvider.selectProject(project);
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => ProjectDetailScreen(
                                          project: project,
                                        ),
                                  ),
                                );
                              },
                              child: CircleAvatar(
                                backgroundColor: Colors.pink[100],
                                radius: 16,
                                child: Icon(Icons.edit, color: Colors.white),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  /// Xây dựng trạng thái trống khi không có dự án
  Widget _buildEmptyProjectState() {
    return Container(
      height: 350, // Đồng bộ với chiều cao PageView
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Card(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.home_work_outlined, size: 80, color: Colors.grey[400]),
              SizedBox(height: 16),
              Text(
                'Chưa có công trình nào',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Nhấn "Thêm mới" để tạo công trình đầu tiên',
                style: TextStyle(fontSize: 16, color: Colors.grey[500]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Xây dựng ảnh mặc định cho dự án
  Widget _buildDefaultProjectImage() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Icon(Icons.home_work, size: 80, color: Colors.white),
      ),
    );
  }

  /// Xây dựng chỉ báo trang
  Widget _buildPageIndicator() {
    return Consumer<ProjectProvider>(
      builder: (context, projectProvider, child) {
        final projects = projectProvider.projects;

        if (projects.isEmpty) {
          return SizedBox.shrink();
        }

        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(projects.length, (index) {
            return Container(
              width: 10,
              height: 10,
              margin: EdgeInsets.symmetric(horizontal: 5),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color:
                    _currentPage == index
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.5),
              ),
            );
          }),
        );
      },
    );
  }

  /// Xây dựng 3 nút menu chính
  Widget _buildMenuOptions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            // Button 1: Thư viện
            _buildMenuButton(
              icon: Icons.library_books,
              label: 'Thư viện',
              index: 0,
              onTap: () {
                setState(() {
                  _selectedMenuIndex = 0;
                });
              },
            ),

            // Button 2: Thêm mới
            _buildMenuButton(
              icon: Icons.add,
              label: 'Thêm mới',
              index: 1,
              onTap: () {
                setState(() {
                  _selectedMenuIndex = 1;
                });
                // Điều hướng đến wizard tạo dự án mới
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ProjectWizardScreen(),
                  ),
                );
              },
            ),

            // Button 3: Tính khối lượng
            _buildMenuButton(
              icon: Icons.calculate,
              label: 'Tính khối lượng',
              index: 2,
              onTap: () {
                setState(() {
                  _selectedMenuIndex = 2;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Xây dựng nút menu
  Widget _buildMenuButton({
    required IconData icon,
    required String label,
    required int index,
    required VoidCallback onTap,
  }) {
    final isSelected = _selectedMenuIndex == index;

    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color:
                  isSelected
                      ? Colors.white
                      : Colors.white.withValues(alpha: 0.8),
              shape: BoxShape.circle,
              boxShadow:
                  isSelected
                      ? [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 8,
                          offset: Offset(0, 4),
                        ),
                      ]
                      : null,
            ),
            child: Icon(
              icon,
              color: Color(0xFF6A3DE8),
              size: isSelected ? 28 : 24,
            ),
          ),
          SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              color: Colors.white,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
              fontSize: isSelected ? 14 : 12,
            ),
          ),
        ],
      ),
    );
  }

  /// Xây dựng nội dung dựa trên menu được chọn
  Widget _buildSelectedContent() {
    switch (_selectedMenuIndex) {
      case 0:
        return _buildMaterialLibraryContent();
      case 1:
        return _buildAddNewContent();
      case 2:
        return _buildCalculateVolumeContent();
      default:
        return _buildMaterialLibraryContent();
    }
  }

  /// Xây dựng nội dung thư viện vật liệu
  Widget _buildMaterialLibraryContent() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Thư viện vật liệu',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Row(
                children: [
                  IconButton(
                    icon: Icon(Icons.download, color: Colors.white),
                    onPressed: () {
                      // Xử lý download
                    },
                  ),
                  SizedBox(width: 8),
                  IconButton(
                    icon: Icon(Icons.upload, color: Colors.white),
                    onPressed: () {
                      // Xử lý upload
                    },
                  ),
                  SizedBox(width: 8),
                  IconButton(
                    icon: Icon(Icons.add, color: Colors.white),
                    tooltip: 'Thêm vật liệu mới',
                    onPressed: () {
                      _showAddMaterialDialog(context);
                    },
                  ),
                ],
              ),
            ],
          ),

          SizedBox(height: 16),

          Expanded(
            child: Consumer<MaterialProvider>(
              builder: (context, materialProvider, child) {
                return SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Materials title
                      Text(
                        'Vật liệu',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      SizedBox(height: 8),

                      // Materials list
                      ...materialProvider.materials
                          .where(
                            (material) =>
                                material.name != 'Nhân công xây dựng' &&
                                material.name != 'Nhân công điện nước',
                          )
                          .map((material) {
                            return _buildMaterialItem(
                              name: material.name,
                              unit: material.unit,
                              price: material.pricePerUnit,
                              color: _getMaterialColor(material.name),
                            );
                          }),

                      SizedBox(height: 16),

                      // Labor title
                      Text(
                        'Nhân công',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      SizedBox(height: 8),

                      // Labor list
                      ...materialProvider.materials
                          .where(
                            (material) =>
                                material.name == 'Nhân công xây dựng' ||
                                material.name == 'Nhân công điện nước',
                          )
                          .map((material) {
                            return _buildMaterialItem(
                              name: material.name,
                              unit: material.unit,
                              price: material.pricePerUnit,
                              color: Colors.pink,
                            );
                          }),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Xây dựng item vật liệu
  Widget _buildMaterialItem({
    required String name,
    required String unit,
    required double price,
    required Color color,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  unit,
                  style: TextStyle(color: Colors.grey[400], fontSize: 12),
                ),
              ],
            ),
          ),
          Text(
            '${NumberFormatter.formatCurrency(price)} VNĐ',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  /// Lấy màu cho vật liệu
  Color _getMaterialColor(String materialName) {
    switch (materialName.toLowerCase()) {
      case 'gạch':
        return Colors.red;
      case 'cát xây':
      case 'cát trát':
        return Colors.orange;
      case 'xi măng':
        return Colors.grey;
      case 'thép':
        return Colors.blue;
      case 'đá':
        return Colors.brown;
      case 'ngói':
        return Colors.deepOrange;
      case 'tôn':
        return Colors.blueGrey;
      case 'thạch cao':
        return Colors.white;
      case 'sơn':
        return Colors.purple;
      case 'cửa nhôm':
      case 'cửa composite':
        return Colors.cyan;
      default:
        return Colors.green;
    }
  }

  /// Xây dựng nội dung thêm mới
  Widget _buildAddNewContent() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      padding: EdgeInsets.all(16),
      child: Center(
        child: Text(
          'Chức năng thêm mới sẽ được triển khai',
          style: TextStyle(color: Colors.white, fontSize: 18),
        ),
      ),
    );
  }

  /// Xây dựng nội dung tính khối lượng
  Widget _buildCalculateVolumeContent() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      padding: EdgeInsets.all(16),
      child: Center(
        child: Text(
          'Chức năng tính khối lượng sẽ được triển khai',
          style: TextStyle(color: Colors.white, fontSize: 18),
        ),
      ),
    );
  }

  /// Hiển thị dialog thêm vật liệu mới
  void _showAddMaterialDialog(BuildContext context) {
    final nameController = TextEditingController();
    final priceController = TextEditingController();
    final unitController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.add_circle, color: Color(0xFF6A3DE8)),
                SizedBox(width: 8),
                Text('Thêm vật liệu mới'),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: nameController,
                    decoration: InputDecoration(
                      labelText: 'Tên vật liệu',
                      prefixIcon: Icon(Icons.inventory),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                  SizedBox(height: 16),
                  TextField(
                    controller: priceController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Giá (VNĐ)',
                      prefixIcon: Icon(Icons.attach_money),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                  SizedBox(height: 16),
                  TextField(
                    controller: unitController,
                    decoration: InputDecoration(
                      labelText: 'Đơn vị (ví dụ: m², kg, viên)',
                      prefixIcon: Icon(Icons.straighten),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Hủy', style: TextStyle(color: Colors.grey[600])),
              ),
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xFF6A3DE8), Color(0xFF4A3DE8)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextButton(
                  onPressed: () {
                    if (nameController.text.isNotEmpty &&
                        priceController.text.isNotEmpty &&
                        unitController.text.isNotEmpty) {
                      _addCustomMaterial(
                        context,
                        nameController.text,
                        double.tryParse(priceController.text) ?? 0.0,
                        unitController.text,
                      );
                      Navigator.pop(context);
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Vui lòng điền đầy đủ thông tin'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                  style: TextButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                  child: Text('Thêm'),
                ),
              ),
            ],
          ),
    );
  }

  /// Thêm vật liệu tùy chỉnh vào thư viện
  void _addCustomMaterial(
    BuildContext context,
    String name,
    double price,
    String unit,
  ) {
    final materialProvider = Provider.of<MaterialProvider>(
      context,
      listen: false,
    );

    try {
      materialProvider.addCustomMaterial(
        name: name,
        pricePerUnit: price,
        customUnit: unit,
        measurementUnit: model.MeasurementUnit.custom,
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Đã thêm vật liệu "$name" vào thư viện'),
          backgroundColor: Color(0xFF6A3DE8),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi khi thêm vật liệu: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
