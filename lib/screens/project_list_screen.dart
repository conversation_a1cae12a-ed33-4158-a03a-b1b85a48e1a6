import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/project/project_provider.dart';
import '../models/project/project_model.dart';
import '../theme/construction_theme.dart';

import '../widgets/gradient_button.dart';
import 'project_wizard/project_wizard_screen.dart';
import 'project_detail_screen.dart';
import 'material_library_screen.dart';

/// Màn hình danh sách dự án
class ProjectListScreen extends StatelessWidget {
  const ProjectListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Danh sách công trình'),
        actions: [
          IconButton(
            icon: const Icon(Icons.inventory_2),
            tooltip: 'Thư viện vật liệu',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MaterialLibraryScreen(),
                ),
              );
            },
          ),
        ],
        elevation: 0,
        flexibleSpace: Builder(
          builder: (context) {
            final isDarkMode =
                MediaQuery.of(context).platformBrightness == Brightness.dark;
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors:
                      isDarkMode
                          ? ConstructionTheme.darkPrimaryGradient
                          : ConstructionTheme.primaryGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            );
          },
        ),
      ),
      body: Consumer<ProjectProvider>(
        builder: (context, projectProvider, child) {
          final projects = projectProvider.projects;

          if (projects.isEmpty) {
            return _buildEmptyState(context);
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: projects.length,
            itemBuilder: (context, index) {
              final project = projects[index];
              return _buildProjectCard(context, project, projectProvider);
            },
          );
        },
      ),
      floatingActionButton: CustomWidgets.circleButton(
        icon: Icons.add,
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ProjectWizardScreen(),
            ),
          );
        },
        backgroundColor: ConstructionTheme.primaryColor,
        iconColor: Colors.white,
      ),
    );
  }

  /// Xây dựng trạng thái trống (không có dự án)
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.home_work_outlined,
            size: 80,
            color: ConstructionTheme.textLightColor,
          ),
          const SizedBox(height: 16),
          Text(
            'Chưa có công trình nào',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Nhấn nút + để tạo công trình mới',
            style: TextStyle(color: ConstructionTheme.textLightColor),
          ),
          const SizedBox(height: 24),
          GradientButton(
            text: 'Tạo công trình mới',
            icon: Icons.add,
            useGradient: true, // Sử dụng hiệu ứng gradient
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ProjectWizardScreen(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// Xây dựng card hiển thị thông tin dự án
  Widget _buildProjectCard(
    BuildContext context,
    Project project,
    ProjectProvider projectProvider,
  ) {
    return Dismissible(
      key: Key(project.id),
      background: Container(
        color: Colors.red,
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: const Icon(Icons.delete, color: Colors.white),
      ),
      secondaryBackground: Container(
        color: ConstructionTheme.primaryColor,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: const Icon(Icons.edit, color: Colors.white),
      ),
      confirmDismiss: (direction) async {
        if (direction == DismissDirection.startToEnd) {
          // Xóa dự án
          return await _showDeleteConfirmDialog(context, project.name);
        } else {
          // Chỉnh sửa dự án
          _showEditDialog(context, project, projectProvider);
          return false;
        }
      },
      onDismissed: (direction) {
        if (direction == DismissDirection.startToEnd) {
          projectProvider.deleteProject(project.id);
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('Đã xóa ${project.name}')));
        }
      },
      child: GestureDetector(
        onTap: () {
          projectProvider.selectProject(project);
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ProjectDetailScreen(project: project),
            ),
          );
        },
        onLongPress: () {
          _showContextMenu(context, project, projectProvider);
        },
        child: Card(
          margin: const EdgeInsets.only(bottom: 16),
          clipBehavior: Clip.antiAlias,
          elevation: 3,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Ảnh đại diện hoặc ảnh mặc định
              SizedBox(
                height: 150,
                width: double.infinity,
                child:
                    project.imagePath != null && project.imagePath!.isNotEmpty
                        ? Image.file(
                          File(project.imagePath!),
                          fit: BoxFit.cover,
                        )
                        : Image.asset(
                          'assets/demo/ux_app.png',
                          fit: BoxFit.cover,
                        ),
              ),

              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      project.name,
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      project.location,
                      style: TextStyle(
                        color: ConstructionTheme.textSecondaryColor,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: 6,
                        horizontal: 10,
                      ),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: ConstructionTheme.accentGradient,
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.calendar_today,
                            size: 16,
                            color: ConstructionTheme.textSecondaryColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _formatDate(project.updatedAt),
                            style: TextStyle(
                              color: ConstructionTheme.textSecondaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Hiển thị dialog xác nhận xóa dự án
  Future<bool> _showDeleteConfirmDialog(
    BuildContext context,
    String projectName,
  ) async {
    return await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Xác nhận xóa'),
                content: Text('Bạn có chắc chắn muốn xóa "$projectName"?'),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: Text(
                      'Hủy',
                      style: TextStyle(
                        color: ConstructionTheme.textSecondaryColor,
                      ),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.red.shade400, Colors.red.shade700],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TextButton(
                      onPressed: () => Navigator.pop(context, true),
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                      ),
                      child: const Text('Xóa'),
                    ),
                  ),
                ],
              ),
        ) ??
        false;
  }

  /// Hiển thị dialog chỉnh sửa dự án
  void _showEditDialog(
    BuildContext context,
    Project project,
    ProjectProvider projectProvider,
  ) {
    final nameController = TextEditingController(text: project.name);
    final locationController = TextEditingController(text: project.location);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Chỉnh sửa công trình'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Tên công trình',
                    prefixIcon: Icon(Icons.home_work),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: locationController,
                  decoration: const InputDecoration(
                    labelText: 'Địa điểm',
                    prefixIcon: Icon(Icons.location_on),
                  ),
                ),
              ],
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'Hủy',
                  style: TextStyle(color: ConstructionTheme.textSecondaryColor),
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: ConstructionTheme.primaryGradient,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextButton(
                  onPressed: () {
                    final updatedProject = Project(
                      id: project.id,
                      name: nameController.text,
                      location: locationController.text,
                      imagePath: project.imagePath,
                      createdAt: project.createdAt,
                      updatedAt: DateTime.now(),
                      floors: project.floors,
                      roof: project.roof,
                      foundationType: project.foundationType,
                      structureType: project.structureType,
                      selectedMaterialIds: project.selectedMaterialIds,
                      detailedParameters: project.detailedParameters,
                      results: project.results,
                    );

                    projectProvider.updateProject(updatedProject);
                    Navigator.pop(context);
                  },
                  style: TextButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                  child: const Text('Lưu'),
                ),
              ),
            ],
          ),
    );
  }

  /// Hiển thị menu ngữ cảnh khi nhấn giữ vào dự án
  void _showContextMenu(
    BuildContext context,
    Project project,
    ProjectProvider projectProvider,
  ) {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(26),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 8, bottom: 16),
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  ListTile(
                    leading: Icon(
                      Icons.edit,
                      color: ConstructionTheme.primaryColor,
                    ),
                    title: const Text('Đổi tên'),
                    onTap: () {
                      Navigator.pop(context);
                      _showRenameDialog(context, project, projectProvider);
                    },
                  ),
                  ListTile(
                    leading: Icon(
                      Icons.copy,
                      color: ConstructionTheme.secondaryColor,
                    ),
                    title: const Text('Sao chép'),
                    onTap: () {
                      Navigator.pop(context);
                      projectProvider.duplicateProject(project);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text('Đã sao chép công trình'),
                          backgroundColor: ConstructionTheme.primaryColor,
                        ),
                      );
                    },
                  ),
                  Container(
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    height: 1,
                    color: Colors.grey.shade200,
                  ),
                  ListTile(
                    leading: const Icon(Icons.delete, color: Colors.red),
                    title: const Text(
                      'Xóa',
                      style: TextStyle(color: Colors.red),
                    ),
                    onTap: () async {
                      Navigator.pop(context);
                      final confirm = await _showDeleteConfirmDialog(
                        context,
                        project.name,
                      );
                      if (confirm) {
                        projectProvider.deleteProject(project.id);
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Đã xóa ${project.name}'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    },
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
          ),
    );
  }

  /// Hiển thị dialog đổi tên dự án
  void _showRenameDialog(
    BuildContext context,
    Project project,
    ProjectProvider projectProvider,
  ) {
    final nameController = TextEditingController(text: project.name);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Đổi tên công trình'),
            content: TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Tên mới',
                prefixIcon: Icon(Icons.edit),
              ),
              autofocus: true,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'Hủy',
                  style: TextStyle(color: ConstructionTheme.textSecondaryColor),
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: ConstructionTheme.primaryGradient,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextButton(
                  onPressed: () {
                    projectProvider.renameProject(
                      project.id,
                      nameController.text,
                    );
                    Navigator.pop(context);
                  },
                  style: TextButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                  child: const Text('Lưu'),
                ),
              ),
            ],
          ),
    );
  }

  /// Định dạng ngày tháng
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
