# Hướng dẫn nâng cấp giao diện Home Screen

## Giới thiệu

Tài liệu này cung cấp mẫu cho AI Agent để nâng cấp giao diện file Home Screen của dự án Flutter hiện tại 

## 1. <PERSON><PERSON><PERSON> về SoftGradientBackground Widget

`SoftGradientBackground` là một widget tái sử dụng có thể tạo hiệu ứng gradient mềm mại cho bất kỳ widget nào. Nó sử dụng kết hợp các kỹ thuật RadialGradient và CustomPainter để tạo ra hiệu ứng chuyển màu mờ ảo giống như được thiết kế trong mẫu.

### Các tính năng chính:

- **Tùy chỉnh linh hoạt**: Hỗ trợ tùy chỉnh cường độ màu, bo tròn góc, và padding
- **<PERSON><PERSON><PERSON> sử dụng**: <PERSON><PERSON> thể áp dụng cho bất kỳ widget nào, từ toàn màn hình đến các thành phần UI nhỏ
- **<PERSON><PERSON> hợp với thiết kế hiện đại**: Tạo cảm giác mềm mại, nổi bật cho UI
- **Hiệu suất tốt**: Được tối ưu để chỉ vẽ lại khi cần thiết

## 2. Implementation Code

```dart
import 'package:flutter/material.dart';
import '../theme/construction_theme.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // Tạo danh sách dự án mẫu
  final List<Map<String, dynamic>> _projects = [
    {
      'name': 'Dự án 1',
      'location': 'Địa điểm 1',
      'image': 'assets/images/project1.jpg',
    },
    {
      'name': 'Dự án 2',
      'location': 'Địa điểm 2',
      'image': 'assets/images/project2.jpg',
    },
    {
      'name': 'Dự án 3',
      'location': 'Địa điểm 3',
      'image': 'assets/images/project3.jpg',
    },
  ];

  int _currentPage = 0;
  
  // Danh sách vật liệu mẫu
  final List<Map<String, dynamic>> _materials = [
    {
      'name': 'Gạch đặc',
      'unit': 'Viên',
      'price': 1400,
      'icon': Icons.circle,
      'color': Colors.pink,
    },
    {
      'name': 'Bê Tông',
      'unit': 'Khối',
      'price': 1100000,
      'icon': Icons.circle,
      'color': Colors.red,
    },
  ];

  // Danh sách nhân công mẫu
  final List<Map<String, dynamic>> _labor = [
    {
      'name': 'Nhân công xây',
      'unit': 'Ngày công',
      'price': 1350000,
      'icon': Icons.circle,
      'color': Colors.pink,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF6A3DE8),
              Color(0xFF4A3DE8),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  children: [
                    IconButton(
                      icon: Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () {
                        // Xử lý khi nhấn nút quay lại
                      },
                    ),
                    Text(
                      'Danh sách công trình',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Spacer(),
                    IconButton(
                      icon: Icon(Icons.more_vert, color: Colors.white),
                      onPressed: () {
                        // Xử lý khi nhấn nút menu
                      },
                    ),
                  ],
                ),
              ),
              
              // Project List
              SizedBox(
                height: 380,
                child: PageView.builder(
                  onPageChanged: (int page) {
                    setState(() {
                      _currentPage = page;
                    });
                  },
                  itemCount: _projects.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Card(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            // Project image
                            ClipRRect(
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(20),
                                topRight: Radius.circular(20),
                              ),
                              child: Container(
                                height: 220,
                                color: Colors.black,
                                child: Center(
                                  child: Image.asset(
                                    'assets/images/mandison_logo.png',
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              ),
                            ),
                            
                            // Project info
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Tên công trình',
                                    style: TextStyle(
                                      fontSize: 32,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.pink[100],
                                    ),
                                  ),
                                  Text(
                                    'Địa điểm',
                                    style: TextStyle(
                                      fontSize: 20,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            
                            // Edit button
                            Align(
                              alignment: Alignment.centerRight,
                              child: Padding(
                                padding: const EdgeInsets.only(right: 16.0),
                                child: CircleAvatar(
                                  backgroundColor: Colors.pink[100],
                                  radius: 25,
                                  child: Icon(
                                    Icons.edit,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              
              // Page indicator
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(_projects.length, (index) {
                  return Container(
                    width: 10,
                    height: 10,
                    margin: EdgeInsets.symmetric(horizontal: 5),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _currentPage == index
                          ? Colors.white
                          : Colors.white.withOpacity(0.5),
                    ),
                  );
                }),
              ),
              
              SizedBox(height: 20),
              
              // Menu options
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      // Button 1: Thư viện
                      Column(
                        children: [
                          Container(
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.library_books,
                              color: Color(0xFF6A3DE8),
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Thư viện',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      
                      // Button 2: Thêm mới
                      Column(
                        children: [
                          Container(
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.add,
                              color: Color(0xFF6A3DE8),
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Thêm mới',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      
                      // Button 3: Tính khối lượng
                      Column(
                        children: [
                          Container(
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.calculate,
                              color: Color(0xFF6A3DE8),
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Tính khối lượng',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              
              SizedBox(height: 20),
              
              // Materials Library Section
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.black87,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Thư viện vật liệu',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Row(
                            children: [
                              Icon(Icons.download, color: Colors.white),
                              SizedBox(width: 16),
                              Icon(Icons.upload, color: Colors.white),
                            ],
                          ),
                        ],
                      ),
                      
                      SizedBox(height: 16),
                      
                      // Materials title
                      Text(
                        'Vật liệu',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      
                      SizedBox(height: 8),
                      
                      // Materials list
                      ..._materials.map((material) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Row(
                            children: [
                              CircleAvatar(
                                backgroundColor: material['color'],
                                radius: 25,
                                child: Icon(
                                  Icons.bubble_chart,
                                  color: Colors.white,
                                ),
                              ),
                              SizedBox(width: 16),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    material['name'],
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    material['unit'],
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                              Spacer(),
                              Text(
                                '${_formatCurrency(material['price'])} VNĐ',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      
                      SizedBox(height: 16),
                      
                      // Labor title
                      Text(
                        'Nhân công',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      
                      SizedBox(height: 8),
                      
                      // Labor list
                      ..._labor.map((labor) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Row(
                            children: [
                              CircleAvatar(
                                backgroundColor: labor['color'],
                                radius: 25,
                                child: Icon(
                                  Icons.bubble_chart,
                                  color: Colors.white,
                                ),
                              ),
                              SizedBox(width: 16),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    labor['name'],
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    labor['unit'],
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                              Spacer(),
                              Text(
                                '${_formatCurrency(labor['price'])} VNĐ',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  // Hàm định dạng tiền tệ
  String _formatCurrency(int value) {
    String priceString = value.toString();
    String result = '';
    int count = 0;
    
    for (int i = priceString.length - 1; i >= 0; i--) {
      count++;
      result = priceString[i] + result;
      if (count % 3 == 0 && i != 0) {
        result = '.$result';
      }
    }
    
    return result;
  }
}